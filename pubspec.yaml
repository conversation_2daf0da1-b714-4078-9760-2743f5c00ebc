name: melodyze
description: "Professional music recording studio in your palm."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.66+66

environment:
  sdk: ^3.5.0

# Explicitly specify the Flutter version to use with FVM
flutter_version: "3.27.3"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  dio: ^5.5.0+1
  get_it: ^8.0.2
  cached_network_image: ^3.4.0
  flutter_svg: ^2.0.10+1
  google_fonts: ^6.2.1
  just_audio: ^0.9.39
  flutter_bloc: ^9.0.0
  equatable: ^2.0.5
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  firebase_core: ^3.5.0
  firebase_auth: ^5.3.0
  firebase_crashlytics: ^4.1.2
  firebase_remote_config: ^5.1.2
  google_sign_in: ^6.2.1
  logger: ^2.2.0
  flutter_secure_storage: ^9.2.2
  aws_signature_v4: ^0.6.1
  path_provider: ^2.1.4
  crypto: ^3.0.3
  focused_menu: ^1.0.5
  keep_screen_on: ^4.0.0
  ffi: ^2.1.2
  permission_handler: ^11.3.1
  scrollable_positioned_list: ^0.3.8
  rxdart: ^0.28.0
  # audio_waveforms: ^1.0.5
  audio_session: ^0.1.21
  device_info_plus: ^11.1.0
  package_info_plus: ^8.1.1
  url_launcher: ^6.3.1
  firebase_messaging: ^15.1.5
  firebase_analytics: ^11.3.5
  flutter_local_notifications: ^18.0.1
  uuid: ^4.5.1
  event_bus: ^2.0.1
  shared_preferences: ^2.3.4
  juce_mix_player:
    git:
      url: https://github.com/chanonly123/juce_mix_player.git
      ref: 3.0.17
      path: juce_mix_player_package
  fluttertoast: ^8.2.12
  shimmer: ^3.0.0
  better_player_plus: ^1.0.8
  intl: 0.19.0
  auto_route: ^10.0.1
  visibility_detector: ^0.4.0+2
  marquee: ^2.3.0
  no_screenshot: ^0.3.1
  flutter_staggered_animations: ^1.1.1
  lottie: ^3.3.1
  animated_splash_screen: ^1.3.0
  flutter_native_splash: ^2.4.4
  video_player: ^2.8.6

dependency_overrides:
  json_annotation: ^4.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.1
  auto_route_generator: ^10.0.1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.12
  json_serializable: ^6.8.0
  freezed: ^2.5.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/juce_configs/
    - assets/svg/
    - assets/png/
    - assets/logo/
    - assets/gif/
    - assets/svg/social_buttons/
    - assets/metronome_tone/
    - assets/filter_icons/
    - assets/splash/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font.
  fonts:
    - family: Ethnocentric
      fonts:
        - asset: assets/fonts/ethnocentric_regular.otf
    - family: Inter
      fonts:
        - asset: assets/fonts/inter.ttc
    - family: Iceland
      fonts:
        - asset: assets/fonts/iceland_regular.ttf
    - family: Outfit
      fonts:
        - asset: assets/fonts/outfit_900.ttf

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
